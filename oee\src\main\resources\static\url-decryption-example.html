<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL参数解密示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input, .input-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .input-group textarea {
            height: 80px;
            resize: vertical;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            color: #004085;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>URL参数解密示例</h1>
        
        <div class="info">
            <strong>说明：</strong>此页面演示如何使用URL参数解密功能。系统会自动检测URL中的加密参数并进行解密。
        </div>

        <div class="section">
            <h3>1. 当前页面URL参数</h3>
            <div class="input-group">
                <label>当前URL:</label>
                <input type="text" id="currentUrl" readonly>
            </div>
            <button class="btn" onclick="parseCurrentUrl()">解析当前URL参数</button>
            <div id="currentUrlResult" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>2. 手动解密URL参数</h3>
            <div class="input-group">
                <label>输入完整URL或加密数据:</label>
                <textarea id="manualUrl" placeholder="输入完整的URL或加密的data参数值"></textarea>
            </div>
            <button class="btn" onclick="parseManualUrl()">解析URL参数</button>
            <div id="manualUrlResult" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>3. 加密状态检查</h3>
            <button class="btn" onclick="checkEncryptionStatus()">检查加密状态</button>
            <div id="encryptionStatusResult" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>4. 测试链接</h3>
            <p>点击以下链接测试URL参数解密功能：</p>
            <div>
                <a href="?data=test" target="_blank">测试链接1（明文参数）</a><br><br>
                <a href="javascript:void(0)" onclick="generateTestLink()">生成加密测试链接</a>
            </div>
            <div id="testLinkResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <!-- 引入URL解密工具 -->
    <script src="/js/url-decryption.js"></script>
    
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 显示当前URL
            document.getElementById('currentUrl').value = window.location.href;
            
            // 监听URL参数解密完成事件
            document.addEventListener('urlParamsDecrypted', function(event) {
                const params = event.detail.params;
                showResult('currentUrlResult', '自动解析结果：\n' + JSON.stringify(params, null, 2), 'success');
            });
        });

        // 解析当前URL参数
        async function parseCurrentUrl() {
            try {
                const params = await window.urlDecryption.parseUrlParams();
                showResult('currentUrlResult', '解析结果：\n' + JSON.stringify(params, null, 2), 'success');
            } catch (error) {
                showResult('currentUrlResult', '解析失败：\n' + error.message, 'error');
            }
        }

        // 解析手动输入的URL参数
        async function parseManualUrl() {
            const input = document.getElementById('manualUrl').value.trim();
            if (!input) {
                showResult('manualUrlResult', '请输入URL或加密数据', 'error');
                return;
            }

            try {
                let params;
                
                // 检查是否是完整URL
                if (input.startsWith('http') || input.includes('://')) {
                    params = await window.urlDecryption.parseUrlParams(input);
                } else if (input.startsWith('?')) {
                    // 查询字符串
                    params = await window.urlDecryption.parseUrlParams(window.location.origin + '/' + input);
                } else {
                    // 可能是加密数据
                    params = await window.urlDecryption.decryptParams(input);
                }
                
                showResult('manualUrlResult', '解析结果：\n' + JSON.stringify(params, null, 2), 'success');
            } catch (error) {
                showResult('manualUrlResult', '解析失败：\n' + error.message, 'error');
            }
        }

        // 检查加密状态
        async function checkEncryptionStatus() {
            try {
                const status = await window.urlDecryption.getEncryptionStatus();
                showResult('encryptionStatusResult', '加密状态：\n' + JSON.stringify(status, null, 2), 'success');
            } catch (error) {
                showResult('encryptionStatusResult', '获取状态失败：\n' + error.message, 'error');
            }
        }

        // 生成测试链接
        function generateTestLink() {
            // 模拟生成一个包含用户信息的URL
            const testParams = {
                userId: 'test123',
                userName: '测试用户'
            };
            
            // 这里只是演示，实际的加密需要在后端进行
            const testUrl = window.location.origin + window.location.pathname + '?userId=' + 
                           encodeURIComponent(testParams.userId) + '&userName=' + 
                           encodeURIComponent(testParams.userName);
            
            showResult('testLinkResult', '测试链接（明文参数）：\n' + testUrl + '\n\n点击链接测试解密功能', 'success');
            
            // 创建可点击的链接
            const linkElement = document.createElement('a');
            linkElement.href = testUrl;
            linkElement.textContent = '点击测试';
            linkElement.target = '_blank';
            
            const resultDiv = document.getElementById('testLinkResult');
            resultDiv.appendChild(document.createElement('br'));
            resultDiv.appendChild(linkElement);
        }

        // 显示结果
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'result ' + type;
            element.style.display = 'block';
        }
    </script>
</body>
</html>
