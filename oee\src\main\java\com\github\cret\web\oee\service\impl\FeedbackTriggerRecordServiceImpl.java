package com.github.cret.web.oee.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.common.exception.SystemException;
import com.github.cret.web.oee.document.feedback.AnomaliesClassification;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerRecord;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerSend;
import com.github.cret.web.oee.document.feedback.NoticeUser;
import com.github.cret.web.oee.document.feedback.Reporter;
import com.github.cret.web.oee.document.feedback.Responder;
import com.github.cret.web.oee.document.feedback.ResponseConfig;
import com.github.cret.web.oee.domain.query.FeedbackTriggerRecordQuery;
import com.github.cret.web.oee.repository.AnomaliesClassificationRepository;
import com.github.cret.web.oee.repository.FeedbackTriggerRecordRepository;
import com.github.cret.web.oee.repository.FeedbackTriggerSendRepository;
import com.github.cret.web.oee.service.FeedbackTriggerRecordService;
import com.github.cret.web.security.domain.AuthUser;
import com.github.cret.web.security.util.SecurityUtil;

@Service
public class FeedbackTriggerRecordServiceImpl implements FeedbackTriggerRecordService {

	private final FeedbackTriggerRecordRepository repository;

	private final AnomaliesClassificationRepository anomaliesClassificationRepository;

	private final FeedbackTriggerSendRepository feedbackTriggerSendRepository;

	private final MongoTemplate mongoTemplate;

	public FeedbackTriggerRecordServiceImpl(FeedbackTriggerRecordRepository repository,
			AnomaliesClassificationRepository anomaliesClassificationRepository,
			FeedbackTriggerSendRepository feedbackTriggerSendRepository, MongoTemplate mongoTemplate) {
		this.repository = repository;
		this.anomaliesClassificationRepository = anomaliesClassificationRepository;
		this.feedbackTriggerSendRepository = feedbackTriggerSendRepository;
		this.mongoTemplate = mongoTemplate;
	}

	@Override
	@Transactional
	public FeedbackTriggerRecord save(FeedbackTriggerRecord record) {
		// 校验必填字段
		if (record.getAnomaliesCode() == null || record.getAnomaliesCode().trim().isEmpty()) {
			throw SysErrEnum.INVALID_PARAMETER.exception("异常编码不能为空");
		}
		if (record.getLineCode() == null || record.getLineCode().trim().isEmpty()) {
			throw SysErrEnum.INVALID_PARAMETER.exception("线体编码不能为空");
		}

		// 设置触发时间，如果未设置则使用当前时间
		if (record.getTriggerTime() == null) {
			record.setTriggerTime(new Date());
		}

		// 设置触发人信息
		AuthUser authUser = SecurityUtil.getCurrentUser();

		if (record.getTriggerUserId() == null) {
			record.setTriggerUserId(authUser.id());
		}

		if (record.getTriggerUserName() == null) {
			record.setTriggerUserName(authUser.name());
		}

		// 初始设置异常未关闭
		record.setTriggerClose(false);

		// 先保存触发记录以获取ID
		FeedbackTriggerRecord savedRecord = repository.save(record);

		// 创建发送记录，根据异常编码和线体编码查找异常分类配置，根据异常分类配置生成发送表
		AnomaliesClassification anomaliesClassification = anomaliesClassificationRepository
			.findByLineCodeAndAnomaliesCode(record.getLineCode(), record.getAnomaliesCode())
			.orElse(null);

		if (anomaliesClassification != null && anomaliesClassification.getResponseConfig() != null) {
			// 获取异常分类配置
			List<ResponseConfig> responseConfigs = anomaliesClassification.getResponseConfig();

			// 为每个响应配置创建发送记录
			for (ResponseConfig config : responseConfigs) {
				createFeedbackTriggerSend(savedRecord, config);
			}
		}

		return savedRecord;
	}

	/**
	 * 创建反馈触发发送记录（为每个用户创建单独记录）
	 * @param triggerRecord 触发记录
	 * @param responseConfig 响应配置
	 */
	private void createFeedbackTriggerSend(FeedbackTriggerRecord triggerRecord, ResponseConfig responseConfig) {
		try {
			// 计算预期发送时间
			Date expectedSendTime = triggerRecord.getTriggerTime();
			if (responseConfig.getPointTime() != null && responseConfig.getPointTime() > 0) {
				// 将pointTime（分钟）转换为毫秒并加到触发时间上
				long pointTimeMillis = responseConfig.getPointTime() * 60 * 1000L;
				expectedSendTime = new Date(triggerRecord.getTriggerTime().getTime() + pointTimeMillis);
			}

			// 构建发送信息
			String sendInfo = buildSendInfo(triggerRecord, responseConfig);

			// 为每个响应人创建单独的发送记录
			if (responseConfig.getResponders() != null) {
				for (Responder responder : responseConfig.getResponders()) {
					if (responder != null && responder.getRespondentId() != null
							&& !responder.getRespondentId().trim().isEmpty()) {
						createSingleUserSendRecord(triggerRecord.getId(), expectedSendTime, sendInfo,
								responder.getRespondentId(), responder.getRespondentName(), "responder");
					}
				}
			}

			// 为每个告知人创建单独的发送记录
			if (responseConfig.getReporters() != null) {
				for (Reporter reporter : responseConfig.getReporters()) {
					if (reporter != null && reporter.getReporterId() != null
							&& !reporter.getReporterId().trim().isEmpty()) {
						createSingleUserSendRecord(triggerRecord.getId(), expectedSendTime, sendInfo,
								reporter.getReporterId(), reporter.getReportName(), "reporter");
					}
				}
			}

		}
		catch (Exception e) {
			// 记录发送失败的情况，但不影响主流程
			createFailedSendRecord(triggerRecord, responseConfig, e);
		}
	}

	/**
	 * 为单个用户创建发送记录
	 * @param triggerRecordId 触发记录ID
	 * @param expectedSendTime 预期发送时间
	 * @param sendInfo 发送信息
	 * @param userId 用户ID
	 * @param userName 用户名称
	 * @param userType 用户类型（responder/reporter）
	 */
	private void createSingleUserSendRecord(String triggerRecordId, Date expectedSendTime, String sendInfo,
			String userId, String userName, String userType) {
		try {
			FeedbackTriggerSend sendRecord = new FeedbackTriggerSend();

			// 设置基本信息
			sendRecord.setTriggerRecordId(triggerRecordId);
			sendRecord.setExpectedSendTime(expectedSendTime);
			sendRecord.setSendInfo(sendInfo);

			// 设置单个用户信息
			sendRecord.setUserId(userId);
			sendRecord.setUserName(userName);
			sendRecord.setUserType(userType);

			// 初始状态：未发送
			sendRecord.setSendStatus(false);

			// 保存发送记录，等待定时任务处理
			feedbackTriggerSendRepository.save(sendRecord);

		}
		catch (Exception e) {
			// 单个用户记录创建失败，记录错误但不影响其他用户
			createFailedSingleUserRecord(triggerRecordId, expectedSendTime, userId, userName, userType, e);
		}
	}

	/**
	 * 创建失败的发送记录
	 * @param triggerRecord 触发记录
	 * @param responseConfig 响应配置
	 * @param e 异常信息
	 */
	private void createFailedSendRecord(FeedbackTriggerRecord triggerRecord, ResponseConfig responseConfig,
			Exception e) {
		try {
			FeedbackTriggerSend failedRecord = new FeedbackTriggerSend();
			failedRecord.setTriggerRecordId(triggerRecord.getId());

			// 设置预期发送时间为触发时间加上pointTime（分钟）
			Date expectedSendTime = triggerRecord.getTriggerTime();
			if (responseConfig.getPointTime() != null && responseConfig.getPointTime() > 0) {
				long pointTimeMillis = responseConfig.getPointTime() * 60 * 1000L;
				expectedSendTime = new Date(triggerRecord.getTriggerTime().getTime() + pointTimeMillis);
			}
			failedRecord.setExpectedSendTime(expectedSendTime);

			failedRecord.setSendResult("创建失败: " + e.getMessage());
			failedRecord.setSendInfo("系统异常，创建发送记录失败");
			failedRecord.setSendStatus(false);
			feedbackTriggerSendRepository.save(failedRecord);
		}
		catch (Exception saveException) {
			// 连保存失败记录都失败了，只能记录日志
			System.err.println("保存失败记录时发生异常: " + saveException.getMessage());
		}
	}

	/**
	 * 创建单个用户的失败记录
	 * @param triggerRecordId 触发记录ID
	 * @param expectedSendTime 预期发送时间
	 * @param userId 用户ID
	 * @param userName 用户名称
	 * @param userType 用户类型
	 * @param e 异常信息
	 */
	private void createFailedSingleUserRecord(String triggerRecordId, Date expectedSendTime, String userId,
			String userName, String userType, Exception e) {
		try {
			FeedbackTriggerSend failedRecord = new FeedbackTriggerSend();
			failedRecord.setTriggerRecordId(triggerRecordId);
			failedRecord.setExpectedSendTime(expectedSendTime);
			failedRecord.setUserId(userId);
			failedRecord.setUserName(userName);
			failedRecord.setUserType(userType);
			failedRecord.setSendResult("创建失败: " + e.getMessage());
			failedRecord.setSendInfo("系统异常，创建单个用户发送记录失败");
			failedRecord.setSendStatus(false);
			feedbackTriggerSendRepository.save(failedRecord);
		}
		catch (Exception saveException) {
			// 连保存失败记录都失败了，只能记录日志
			System.err.println("保存单个用户失败记录时发生异常: " + saveException.getMessage());
		}
	}

	/**
	 * 构建发送信息
	 * @param triggerRecord 触发记录
	 * @param responseConfig 响应配置
	 * @return 发送信息
	 */
	private String buildSendInfo(FeedbackTriggerRecord triggerRecord, ResponseConfig responseConfig) {
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分ss秒");

		return """
				异常告警通知
				线体编码: %s
				异常名称：%s
				异常详情：%s
				触发时间: %s
				触发人: %s
				响应时间要求: %s
				""".formatted(triggerRecord.getLineCode(), triggerRecord.getAnomaliesName(),
				triggerRecord.getAnomaliesDetail(), dateFormat.format(triggerRecord.getTriggerTime()),
				triggerRecord.getTriggerUserName(), responseConfig.getPointTime() + "分钟内");
	}

	@Override
	public void delete(String id) {
		repository.deleteById(id);
	}

	@Override
	public FeedbackTriggerRecord findById(String id) {
		return repository.findById(id).orElseThrow(() -> new SystemException(SysErrEnum.NOT_FOUND));
	}

	@Override
	public PageList<FeedbackTriggerRecord> search(FeedbackTriggerRecordQuery query) {
		Query mongoQuery = new Query();
		List<Criteria> criteria = new ArrayList<>();

		if (StringUtils.hasText(query.getAnomaliesCode())) {
			criteria.add(Criteria.where("anomaliesCode").is(query.getAnomaliesCode()));
		}

		if (StringUtils.hasText(query.getLineCode())) {
			criteria.add(Criteria.where("lineCode").is(query.getLineCode()));
		}

		if (StringUtils.hasText(query.getTriggerId())) {
			criteria.add(Criteria.where("triggerId").is(query.getTriggerId()));
		}

		if (query.getStartTime() != null) {
			criteria.add(Criteria.where("triggerTime").gte(query.getStartTime()));
		}

		if (query.getEndTime() != null) {
			criteria.add(Criteria.where("triggerTime").lte(query.getEndTime()));
		}

		if (!criteria.isEmpty()) {
			mongoQuery.addCriteria(new Criteria().andOperator(criteria.toArray(new Criteria[0])));
		}

		long total = mongoTemplate.count(mongoQuery, FeedbackTriggerRecord.class);

		mongoQuery.with(query.getPageRequest());
		List<FeedbackTriggerRecord> list = mongoTemplate.find(mongoQuery, FeedbackTriggerRecord.class);

		PageList<FeedbackTriggerRecord> result = new PageList<>();
		result.setList(list);
		result.setTotal(total);
		result.setHasNext((long) query.getPageRequest().getOffset() + query.getPageRequest().getPageSize() < total);

		return result;
	}

	@Override
	public FeedbackTriggerRecord update(String id, FeedbackTriggerRecord record) {
		FeedbackTriggerRecord existing = findById(id);
		record.setId(existing.getId());
		return repository.save(record);
	}

	@Override
	public boolean hasOpenExceptions(String lineCode) {
		Query query = new Query();
		query.addCriteria(Criteria.where("lineCode").is(lineCode).and("triggerClose").is(false));
		return mongoTemplate.exists(query, FeedbackTriggerRecord.class);
	}

	@Override
	public FeedbackTriggerRecord findLatestOpenException(String lineCode) {
		Query query = new Query();
		query.addCriteria(Criteria.where("lineCode").is(lineCode).and("triggerClose").is(false));
		query.with(Sort.by(Sort.Direction.DESC, "triggerTime"));
		query.limit(1);
		return mongoTemplate.findOne(query, FeedbackTriggerRecord.class);
	}

	@Override
	public FeedbackTriggerRecord closeException(String id) {
		return repository.findById(id).map(record -> {
			record.setTriggerClose(true);
			record.setTriggerCloseTime(new Date());
			return repository.save(record);
		}).orElseThrow(() -> new SystemException(SysErrEnum.NOT_FOUND));
	}

	@Override
	public List<NoticeUser> getNoticeUsers(String id) {
		FeedbackTriggerRecord record = findById(id);
		List<NoticeUser> noticeUsers = record.getNoticeUsers();
		return noticeUsers != null ? noticeUsers : new ArrayList<>();
	}

}
