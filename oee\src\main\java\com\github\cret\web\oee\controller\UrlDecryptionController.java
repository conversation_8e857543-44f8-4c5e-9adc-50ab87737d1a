package com.github.cret.web.oee.controller;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.oee.config.FeedbackConfig;

/**
 * URL解密控制器
 * 为前端提供URL查询参数解密服务
 */
@RestController
@RequestMapping("/url")
public class UrlDecryptionController {

    private static final Logger logger = LoggerFactory.getLogger(UrlDecryptionController.class);
    
    private final FeedbackConfig feedbackConfig;
    
    public UrlDecryptionController(FeedbackConfig feedbackConfig) {
        this.feedbackConfig = feedbackConfig;
    }
    
    /**
     * 解密URL查询参数
     * @param data 加密的参数数据
     * @return 解密后的参数Map
     */
    @GetMapping("/decrypt")
    public ResponseEntity<Map<String, Object>> decryptParams(@RequestParam(required = false) String data) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 检查是否启用了URL加密
            if (!feedbackConfig.isUrlEncryptionEnabled()) {
                response.put("success", false);
                response.put("message", "URL加密未启用");
                response.put("data", new HashMap<>());
                return ResponseEntity.ok(response);
            }
            
            // 检查参数
            if (data == null || data.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "缺少加密数据参数");
                response.put("data", new HashMap<>());
                return ResponseEntity.badRequest().body(response);
            }
            
            // 解密参数
            Map<String, String> decryptedParams = feedbackConfig.decryptUrlParams(data);
            
            response.put("success", true);
            response.put("message", "解密成功");
            response.put("data", decryptedParams);
            
            logger.debug("URL参数解密成功，参数数量: {}", decryptedParams.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("URL参数解密失败", e);
            
            response.put("success", false);
            response.put("message", "解密失败: " + e.getMessage());
            response.put("data", new HashMap<>());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 检查URL加密状态
     * @return 加密状态信息
     */
    @GetMapping("/encryption-status")
    public ResponseEntity<Map<String, Object>> getEncryptionStatus() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean isEnabled = feedbackConfig.isUrlEncryptionEnabled();
            
            response.put("success", true);
            response.put("message", "获取加密状态成功");
            response.put("data", Map.of(
                "enabled", isEnabled,
                "description", isEnabled ? "URL参数加密已启用" : "URL参数加密未启用"
            ));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取URL加密状态失败", e);
            
            response.put("success", false);
            response.put("message", "获取加密状态失败: " + e.getMessage());
            response.put("data", new HashMap<>());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
