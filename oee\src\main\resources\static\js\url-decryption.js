/**
 * URL参数解密工具
 * 为前端提供URL查询参数解密功能
 */
class UrlDecryption {
    
    constructor(baseUrl = '') {
        this.baseUrl = baseUrl || window.location.origin;
        this.apiEndpoint = '/api/url/decrypt';
        this.statusEndpoint = '/api/url/encryption-status';
    }
    
    /**
     * 从URL中提取加密的查询参数
     * @param {string} url - 完整的URL或查询字符串
     * @returns {string|null} 加密的数据参数
     */
    extractEncryptedData(url = window.location.href) {
        try {
            const urlObj = new URL(url);
            return urlObj.searchParams.get('data');
        } catch (error) {
            // 如果不是完整URL，尝试作为查询字符串处理
            const params = new URLSearchParams(url.startsWith('?') ? url : '?' + url);
            return params.get('data');
        }
    }
    
    /**
     * 解密URL查询参数
     * @param {string} encryptedData - 加密的参数数据
     * @returns {Promise<Object>} 解密后的参数对象
     */
    async decryptParams(encryptedData) {
        if (!encryptedData) {
            throw new Error('缺少加密数据参数');
        }
        
        try {
            const response = await fetch(`${this.baseUrl}${this.apiEndpoint}?data=${encodeURIComponent(encryptedData)}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });
            
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.message || '解密失败');
            }
            
            return result.data;
            
        } catch (error) {
            console.error('URL参数解密失败:', error);
            throw error;
        }
    }
    
    /**
     * 从当前页面URL解密查询参数
     * @returns {Promise<Object>} 解密后的参数对象
     */
    async decryptCurrentUrlParams() {
        const encryptedData = this.extractEncryptedData();
        if (!encryptedData) {
            return {};
        }
        
        return await this.decryptParams(encryptedData);
    }
    
    /**
     * 检查URL加密状态
     * @returns {Promise<Object>} 加密状态信息
     */
    async getEncryptionStatus() {
        try {
            const response = await fetch(`${this.baseUrl}${this.statusEndpoint}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });
            
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.message || '获取加密状态失败');
            }
            
            return result.data;
            
        } catch (error) {
            console.error('获取URL加密状态失败:', error);
            throw error;
        }
    }
    
    /**
     * 解析URL参数（支持加密和明文）
     * @param {string} url - 要解析的URL，默认为当前页面URL
     * @returns {Promise<Object>} 解析后的参数对象
     */
    async parseUrlParams(url = window.location.href) {
        try {
            // 首先尝试解密参数
            const encryptedData = this.extractEncryptedData(url);
            if (encryptedData) {
                try {
                    return await this.decryptParams(encryptedData);
                } catch (decryptError) {
                    console.warn('解密失败，尝试解析明文参数:', decryptError);
                }
            }
            
            // 如果没有加密数据或解密失败，解析明文参数
            const urlObj = new URL(url);
            const params = {};
            urlObj.searchParams.forEach((value, key) => {
                if (key !== 'data') { // 排除加密数据参数
                    params[key] = decodeURIComponent(value);
                }
            });
            
            return params;
            
        } catch (error) {
            console.error('解析URL参数失败:', error);
            return {};
        }
    }
}

// 创建全局实例
window.urlDecryption = new UrlDecryption();

// 提供便捷的全局函数
window.decryptUrlParams = async function(encryptedData) {
    return await window.urlDecryption.decryptParams(encryptedData);
};

window.parseCurrentUrlParams = async function() {
    return await window.urlDecryption.parseUrlParams();
};

// 页面加载完成后自动解析URL参数（可选）
document.addEventListener('DOMContentLoaded', async function() {
    try {
        const params = await window.urlDecryption.parseUrlParams();
        console.log('页面URL参数:', params);
        
        // 将参数存储到全局变量中，方便其他脚本使用
        window.urlParams = params;
        
        // 触发自定义事件，通知其他脚本参数已解析完成
        const event = new CustomEvent('urlParamsDecrypted', { 
            detail: { params: params } 
        });
        document.dispatchEvent(event);
        
    } catch (error) {
        console.error('自动解析URL参数失败:', error);
        window.urlParams = {};
    }
});
